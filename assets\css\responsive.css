/* Dev Frames - Responsive Styles */

/* Tablet Styles */
@media (max-width: 1024px) {
  .container {
    padding: 0 var(--spacing-lg);
  }
  
  .app-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .customization-panel {
    position: static;
    max-height: none;
    order: -1;
  }
  
  .customization-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-base);
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    flex-direction: column;
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
    box-shadow: 0 4px 6px var(--shadow-light);
  }
  
  .nav-menu.active {
    display: flex;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }
  
  .mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
  }
  
  .mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }
  
  .container {
    padding: 0 var(--spacing-md);
  }
  
  .main-content {
    padding: var(--spacing-lg) 0;
  }
  
  .hero {
    margin-bottom: var(--spacing-xl);
  }
  
  .hero-title {
    font-size: var(--font-size-2xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-sm);
  }
  
  .app-layout {
    gap: var(--spacing-md);
  }
  
  .panel-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .panel-title {
    font-size: var(--font-size-base);
  }
  
  .code-editor {
    min-height: 300px;
    font-size: var(--font-size-xs);
  }
  
  .customization-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .control-group {
    margin-bottom: var(--spacing-md);
  }
  
  .preview-container {
    padding: var(--spacing-md);
  }
  
  .code-preview {
    max-width: 100%;
    overflow-x: auto;
  }
  
  .frame-content {
    padding: var(--spacing-md);
  }
  
  .frame-content pre {
    font-size: var(--font-size-xs);
  }
  
  .footer-content {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }
  
  .footer-links {
    justify-content: center;
  }
  
  .btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .editor-actions,
  .preview-actions {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
  .nav-container {
    padding: 0 var(--spacing-sm);
  }
  
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .brand-text {
    display: none;
  }
  
  .nav-link span {
    display: none;
  }
  
  .hero-title {
    font-size: var(--font-size-xl);
  }
  
  .code-editor {
    min-height: 250px;
  }
  
  .frame-header {
    padding: var(--spacing-sm);
  }
  
  .frame-title {
    font-size: var(--font-size-xs);
  }
  
  .frame-controls {
    gap: var(--spacing-xs);
  }
  
  .frame-control {
    width: 10px;
    height: 10px;
  }
  
  .footer {
    padding: var(--spacing-lg) 0;
  }
}

/* Print Styles */
@media print {
  .navbar,
  .footer,
  .customization-panel,
  .editor-panel .panel-header,
  .preview-panel .panel-header {
    display: none;
  }
  
  .main-content {
    padding: 0;
  }
  
  .app-layout {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .preview-container {
    padding: 0;
    background: white;
  }
  
  .code-preview {
    box-shadow: none;
    border: 1px solid #000;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.5);
  }
  
  .theme-night {
    --border-color: #ffffff;
    --text-secondary: #ffffff;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}

/* Focus Styles for Accessibility */
@media (prefers-reduced-motion: no-preference) {
  .btn:focus,
  .nav-link:focus,
  .theme-toggle:focus,
  .control-input:focus,
  .language-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

/* Dark Mode Preference */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #06b6d4;
    --primary-hover: #0891b2;
    --secondary-color: #374151;
    --accent-color: #22d3ee;
    
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --bg-code: #1e293b;
    
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --text-inverse: #111827;
    
    --border-color: #374151;
    --border-hover: #4b5563;
    --shadow-light: rgba(0, 0, 0, 0.2);
    --shadow-medium: rgba(0, 0, 0, 0.3);
    --shadow-heavy: rgba(0, 0, 0, 0.4);
  }
}

/* About Page Responsive Styles */
@media (max-width: 768px) {
  .about-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .developer-card {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .developer-links {
    justify-content: center;
  }

  .why-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .cta-section {
    padding: var(--spacing-xl);
  }

  .cta-title {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  .about-title {
    font-size: var(--font-size-3xl);
  }

  .about-card {
    padding: var(--spacing-lg);
  }

  .developer-card {
    padding: var(--spacing-lg);
  }

  .developer-links {
    flex-direction: column;
    align-items: center;
  }

  .developer-link {
    width: 100%;
    justify-content: center;
    max-width: 200px;
  }
}
