<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Dev Frames - Create beautiful, framed images of your code snippets. Perfect for Arduino, ESP8266/ESP32, and all programming languages. Professional code presentation tool for developers.">
    <meta name="keywords" content="code frames, code snippets, Arduino code, ESP32, programming, developer tools, code presentation, syntax highlighting">
    <meta name="author" content="SK Raihan - SKR Electronics Lab">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Dev Frames - Beautiful Code Snippet Images">
    <meta property="og:description" content="Create stunning, framed images of your code snippets with support for Arduino, ESP8266/ESP32, and all major programming languages.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://devframes.com">
    <meta property="og:image" content="assets/images/og-image.png">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Dev Frames - Beautiful Code Snippet Images">
    <meta name="twitter:description" content="Create stunning, framed images of your code snippets with support for Arduino, ESP8266/ESP32, and all major programming languages.">
    <meta name="twitter:image" content="assets/images/twitter-card.png">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💻</text></svg>">
    
    <title>Dev Frames - Create Beautiful Code Snippet Images</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Fira+Code:wght@300;400;500;600&family=JetBrains+Mono:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Prism.js for Syntax Highlighting -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    
    <!-- AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXX" crossorigin="anonymous"></script>
</head>
<body class="theme-day">
    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="main navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="#" class="brand-link" aria-label="Dev Frames Home">
                    <i class="fas fa-code" aria-hidden="true"></i>
                    <span class="brand-text">Dev Frames</span>
                </a>
            </div>
            
            <div class="nav-menu">
                <a href="#home" class="nav-link active" aria-current="page">
                    <i class="fas fa-home" aria-hidden="true"></i>
                    <span>Home</span>
                </a>
                <a href="#about" class="nav-link">
                    <i class="fas fa-info-circle" aria-hidden="true"></i>
                    <span>About</span>
                </a>
                <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark mode">
                    <i class="fas fa-moon" aria-hidden="true"></i>
                </button>
            </div>
            
            <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle mobile menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Home Section -->
        <section id="home" class="section active">
            <div class="container">
                <header class="hero">
                    <h1 class="hero-title">Create Beautiful Code Frames</h1>
                    <p class="hero-subtitle">Transform your code snippets into stunning, professional images perfect for sharing, teaching, and presentations.</p>
                </header>
                
                <div class="app-layout">
                    <!-- Code Editor Panel -->
                    <div class="editor-panel">
                        <div class="panel-header">
                            <h2 class="panel-title">
                                <i class="fas fa-code" aria-hidden="true"></i>
                                Code Editor
                            </h2>
                            <div class="editor-actions">
                                <button class="btn btn-secondary" id="clearCode" aria-label="Clear code">
                                    <i class="fas fa-trash" aria-hidden="true"></i>
                                </button>
                                <button class="btn btn-secondary" id="undoCode" aria-label="Undo">
                                    <i class="fas fa-undo" aria-hidden="true"></i>
                                </button>
                                <button class="btn btn-secondary" id="redoCode" aria-label="Redo">
                                    <i class="fas fa-redo" aria-hidden="true"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="editor-container">
                            <textarea 
                                id="codeEditor" 
                                class="code-editor" 
                                placeholder="Paste your code here... Arduino, ESP32, Python, JavaScript, and more!"
                                aria-label="Code editor"
                                spellcheck="false"
                            ></textarea>
                        </div>
                        
                        <div class="editor-footer">
                            <div class="language-selector">
                                <label for="languageSelect" class="sr-only">Select programming language</label>
                                <select id="languageSelect" class="language-select" aria-label="Programming language">
                                    <option value="auto">Auto-detect</option>
                                    <option value="arduino">Arduino</option>
                                    <option value="cpp">C++</option>
                                    <option value="c">C</option>
                                    <option value="python">Python</option>
                                    <option value="javascript">JavaScript</option>
                                    <option value="html">HTML</option>
                                    <option value="css">CSS</option>
                                    <option value="java">Java</option>
                                    <option value="json">JSON</option>
                                    <option value="xml">XML</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Customization Panel -->
                    <div class="customization-panel">
                        <div class="panel-header">
                            <h2 class="panel-title">
                                <i class="fas fa-palette" aria-hidden="true"></i>
                                Customize
                            </h2>
                        </div>
                        
                        <div class="customization-content">
                            <!-- Customization controls will be added here -->
                        </div>
                    </div>
                    
                    <!-- Preview Panel -->
                    <div class="preview-panel">
                        <div class="panel-header">
                            <h2 class="panel-title">
                                <i class="fas fa-eye" aria-hidden="true"></i>
                                Preview
                            </h2>
                            <div class="preview-actions">
                                <button class="btn btn-primary" id="exportPNG" aria-label="Export as PNG">
                                    <i class="fas fa-download" aria-hidden="true"></i>
                                    PNG
                                </button>
                                <button class="btn btn-secondary" id="exportSVG" aria-label="Export as SVG">
                                    <i class="fas fa-vector-square" aria-hidden="true"></i>
                                    SVG
                                </button>
                            </div>
                        </div>
                        
                        <div class="preview-container">
                            <div id="codePreview" class="code-preview">
                                <!-- Code preview will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- About Section -->
        <section id="about" class="section">
            <div class="container">
                <div class="about-content">
                    <header class="about-header">
                        <h1 class="about-title">About Dev Frames</h1>
                        <p class="about-subtitle">The ultimate tool for creating beautiful code snippet images</p>
                    </header>

                    <div class="about-grid">
                        <!-- Features Section -->
                        <div class="about-card">
                            <div class="card-icon">
                                <i class="fas fa-code" aria-hidden="true"></i>
                            </div>
                            <h2 class="card-title">Unique Arduino Support</h2>
                            <p class="card-description">
                                Dev Frames is the only platform that provides dedicated syntax highlighting and styling
                                for Arduino, ESP8266, and ESP32 code out of the box. Perfect for electronics enthusiasts
                                and IoT developers.
                            </p>
                        </div>

                        <div class="about-card">
                            <div class="card-icon">
                                <i class="fas fa-palette" aria-hidden="true"></i>
                            </div>
                            <h2 class="card-title">Advanced Customization</h2>
                            <p class="card-description">
                                Choose from multiple themes, adjust fonts, spacing, window styles, and more.
                                Create the perfect frame that matches your brand or presentation style.
                            </p>
                        </div>

                        <div class="about-card">
                            <div class="card-icon">
                                <i class="fas fa-download" aria-hidden="true"></i>
                            </div>
                            <h2 class="card-title">Multiple Export Options</h2>
                            <p class="card-description">
                                Export your code frames as high-quality PNG images or scalable SVG files.
                                Perfect for blogs, presentations, social media, and documentation.
                            </p>
                        </div>

                        <div class="about-card">
                            <div class="card-icon">
                                <i class="fas fa-mobile-alt" aria-hidden="true"></i>
                            </div>
                            <h2 class="card-title">Responsive Design</h2>
                            <p class="card-description">
                                Works seamlessly on desktop, tablet, and mobile devices. Create and customize
                                your code frames anywhere, anytime.
                            </p>
                        </div>

                        <div class="about-card">
                            <div class="card-icon">
                                <i class="fas fa-bolt" aria-hidden="true"></i>
                            </div>
                            <h2 class="card-title">Fast & Lightweight</h2>
                            <p class="card-description">
                                Built with performance in mind. No heavy frameworks, just clean, efficient code
                                that loads quickly and runs smoothly.
                            </p>
                        </div>

                        <div class="about-card">
                            <div class="card-icon">
                                <i class="fas fa-heart" aria-hidden="true"></i>
                            </div>
                            <h2 class="card-title">Free & Open</h2>
                            <p class="card-description">
                                Completely free to use with no registration required. Create unlimited code frames
                                and share them however you like.
                            </p>
                        </div>
                    </div>

                    <!-- Developer Section -->
                    <div class="developer-section">
                        <div class="developer-card">
                            <div class="developer-avatar">
                                <i class="fas fa-user-circle" aria-hidden="true"></i>
                            </div>
                            <div class="developer-info">
                                <h2 class="developer-name">SK Raihan</h2>
                                <p class="developer-title">Electronics Engineering Student & Developer</p>
                                <p class="developer-description">
                                    Founder of SKR Electronics Lab, passionate about electronics, programming,
                                    and creating tools that help developers showcase their work beautifully.
                                </p>

                                <div class="developer-links">
                                    <a href="mailto:<EMAIL>" class="developer-link" aria-label="Email SK Raihan">
                                        <i class="fas fa-envelope" aria-hidden="true"></i>
                                        <span>Email</span>
                                    </a>
                                    <a href="https://instagram.com/skr_electronics_lab" target="_blank" rel="noopener" class="developer-link" aria-label="Instagram">
                                        <i class="fab fa-instagram" aria-hidden="true"></i>
                                        <span>Instagram</span>
                                    </a>
                                    <a href="https://youtube.com/c/SKRElectronicsLab" target="_blank" rel="noopener" class="developer-link" aria-label="YouTube Channel">
                                        <i class="fab fa-youtube" aria-hidden="true"></i>
                                        <span>YouTube</span>
                                    </a>
                                    <a href="https://twitter.com/skrelectronics" target="_blank" rel="noopener" class="developer-link" aria-label="Twitter">
                                        <i class="fab fa-twitter" aria-hidden="true"></i>
                                        <span>Twitter</span>
                                    </a>
                                    <a href="https://skrelectronicslab.com" target="_blank" rel="noopener" class="developer-link" aria-label="Website">
                                        <i class="fas fa-globe" aria-hidden="true"></i>
                                        <span>Website</span>
                                    </a>
                                    <a href="https://buymeacoffee.com/skrelectronics" target="_blank" rel="noopener" class="developer-link support-link" aria-label="Buy Me a Coffee">
                                        <i class="fas fa-coffee" aria-hidden="true"></i>
                                        <span>Buy Me a Coffee</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Why Choose Dev Frames -->
                    <div class="why-section">
                        <h2 class="section-title">Why Choose Dev Frames?</h2>
                        <div class="why-grid">
                            <div class="why-item">
                                <h3>Perfect for Teaching</h3>
                                <p>Create clear, professional code examples for tutorials, courses, and educational content.</p>
                            </div>
                            <div class="why-item">
                                <h3>Social Media Ready</h3>
                                <p>Generate eye-catching code snippets that stand out on Twitter, LinkedIn, and other platforms.</p>
                            </div>
                            <div class="why-item">
                                <h3>Blog & Documentation</h3>
                                <p>Enhance your technical blog posts and documentation with beautiful, readable code frames.</p>
                            </div>
                            <div class="why-item">
                                <h3>Presentations</h3>
                                <p>Make your code presentations more engaging with professionally styled code snippets.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Call to Action -->
                    <div class="cta-section">
                        <h2 class="cta-title">Ready to Create Beautiful Code Frames?</h2>
                        <p class="cta-description">Start creating stunning code snippet images in seconds!</p>
                        <a href="#home" class="btn btn-primary btn-large">
                            <i class="fas fa-rocket" aria-hidden="true"></i>
                            Get Started Now
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <i class="fas fa-code" aria-hidden="true"></i>
                    <span>Dev Frames</span>
                </div>
                <div class="footer-links">
                    <a href="mailto:<EMAIL>" aria-label="Email SK Raihan">
                        <i class="fas fa-envelope" aria-hidden="true"></i>
                    </a>
                    <a href="https://instagram.com/skr_electronics_lab" target="_blank" rel="noopener" aria-label="Instagram">
                        <i class="fab fa-instagram" aria-hidden="true"></i>
                    </a>
                    <a href="https://youtube.com/c/SKRElectronicsLab" target="_blank" rel="noopener" aria-label="YouTube">
                        <i class="fab fa-youtube" aria-hidden="true"></i>
                    </a>
                    <a href="https://twitter.com/skrelectronics" target="_blank" rel="noopener" aria-label="Twitter">
                        <i class="fab fa-twitter" aria-hidden="true"></i>
                    </a>
                    <a href="https://buymeacoffee.com/skrelectronics" target="_blank" rel="noopener" aria-label="Buy Me a Coffee">
                        <i class="fas fa-coffee" aria-hidden="true"></i>
                    </a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Dev Frames by <a href="https://skrelectronicslab.com" target="_blank" rel="noopener">SK Raihan - SKR Electronics Lab</a></p>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script src="assets/js/editor.js"></script>
    <script src="assets/js/export.js"></script>
</body>
</html>
