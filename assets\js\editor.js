// Dev Frames - Editor Functionality

class CodeEditor {
  constructor(devFrames) {
    this.devFrames = devFrames;
    this.history = [];
    this.historyIndex = -1;
    this.maxHistory = 50;
    
    this.init();
  }
  
  init() {
    this.setupEditorEnhancements();
    this.setupCustomizationControls();
    this.addArduinoSupport();
  }
  
  setupEditorEnhancements() {
    const codeEditor = document.getElementById('codeEditor');
    if (!codeEditor) return;
    
    // Add tab support
    codeEditor.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        e.preventDefault();
        const start = codeEditor.selectionStart;
        const end = codeEditor.selectionEnd;
        
        // Insert tab character
        codeEditor.value = codeEditor.value.substring(0, start) + 
                          '  ' + 
                          codeEditor.value.substring(end);
        
        // Move cursor
        codeEditor.selectionStart = codeEditor.selectionEnd = start + 2;
      }
    });
    
    // Auto-save to history
    codeEditor.addEventListener('input', () => {
      this.saveToHistory(codeEditor.value);
    });
    
    // Auto-closing brackets
    codeEditor.addEventListener('input', (e) => {
      this.handleAutoClosing(e);
    });
  }
  
  setupCustomizationControls() {
    this.createCustomizationPanel();
  }
  
  createCustomizationPanel() {
    const customizationContent = document.querySelector('.customization-content');
    if (!customizationContent) return;
    
    const controlsHTML = `
      <!-- Theme Selection -->
      <div class="control-group">
        <label class="control-label" for="codeThemeSelect">Code Theme</label>
        <select id="codeThemeSelect" class="control-input">
          <option value="default">Default Light</option>
          <option value="monokai">Monokai</option>
          <option value="dracula">Dracula</option>
          <option value="solarized-light">Solarized Light</option>
          <option value="solarized-dark">Solarized Dark</option>
        </select>
      </div>
      
      <!-- Font Settings -->
      <div class="control-group">
        <label class="control-label" for="fontFamilySelect">Font Family</label>
        <select id="fontFamilySelect" class="control-input">
          <option value="Fira Code">Fira Code</option>
          <option value="JetBrains Mono">JetBrains Mono</option>
          <option value="Monaco">Monaco</option>
          <option value="Consolas">Consolas</option>
          <option value="Courier New">Courier New</option>
        </select>
      </div>
      
      <div class="control-group">
        <label class="control-label" for="fontSizeRange">Font Size: <span id="fontSizeValue">14</span>px</label>
        <input type="range" id="fontSizeRange" class="control-range" min="10" max="24" value="14">
      </div>
      
      <!-- Spacing Settings -->
      <div class="control-group">
        <label class="control-label" for="paddingRange">Padding: <span id="paddingValue">20</span>px</label>
        <input type="range" id="paddingRange" class="control-range" min="10" max="50" value="20">
      </div>
      
      <div class="control-group">
        <label class="control-label" for="borderRadiusRange">Border Radius: <span id="borderRadiusValue">8</span>px</label>
        <input type="range" id="borderRadiusRange" class="control-range" min="0" max="20" value="8">
      </div>
      
      <!-- Window Style -->
      <div class="control-group">
        <label class="control-label" for="windowStyleSelect">Window Style</label>
        <select id="windowStyleSelect" class="control-input">
          <option value="macos">macOS</option>
          <option value="windows">Windows</option>
          <option value="minimal">Minimal</option>
          <option value="none">None</option>
        </select>
      </div>
      
      <!-- Title -->
      <div class="control-group">
        <label class="control-label" for="titleInput">Custom Title</label>
        <input type="text" id="titleInput" class="control-input" placeholder="Enter custom title...">
      </div>
      
      <!-- Options -->
      <div class="control-group">
        <label class="control-checkbox">
          <input type="checkbox" id="lineNumbersCheck" checked>
          <span>Show Line Numbers</span>
        </label>
      </div>
      
      <div class="control-group">
        <label class="control-checkbox">
          <input type="checkbox" id="shadowCheck" checked>
          <span>Drop Shadow</span>
        </label>
      </div>
      
      <!-- Preset Actions -->
      <div class="control-group">
        <button class="btn btn-secondary" id="resetSettings" style="width: 100%; margin-bottom: 8px;">
          <i class="fas fa-undo"></i> Reset to Default
        </button>
        <button class="btn btn-primary" id="savePreset" style="width: 100%;">
          <i class="fas fa-save"></i> Save Preset
        </button>
      </div>
    `;
    
    customizationContent.innerHTML = controlsHTML;
    this.bindCustomizationEvents();
  }
  
  bindCustomizationEvents() {
    // Theme selection
    const codeThemeSelect = document.getElementById('codeThemeSelect');
    if (codeThemeSelect) {
      codeThemeSelect.addEventListener('change', (e) => {
        this.devFrames.currentCodeTheme = e.target.value;
        this.devFrames.updatePreview();
      });
    }
    
    // Font family
    const fontFamilySelect = document.getElementById('fontFamilySelect');
    if (fontFamilySelect) {
      fontFamilySelect.addEventListener('change', (e) => {
        this.devFrames.settings.fontFamily = e.target.value;
        this.devFrames.updatePreview();
        this.devFrames.saveSettings();
      });
    }
    
    // Font size
    const fontSizeRange = document.getElementById('fontSizeRange');
    const fontSizeValue = document.getElementById('fontSizeValue');
    if (fontSizeRange && fontSizeValue) {
      fontSizeRange.addEventListener('input', (e) => {
        const size = parseInt(e.target.value);
        this.devFrames.settings.fontSize = size;
        fontSizeValue.textContent = size;
        this.devFrames.updatePreview();
        this.devFrames.saveSettings();
      });
    }
    
    // Padding
    const paddingRange = document.getElementById('paddingRange');
    const paddingValue = document.getElementById('paddingValue');
    if (paddingRange && paddingValue) {
      paddingRange.addEventListener('input', (e) => {
        const padding = parseInt(e.target.value);
        this.devFrames.settings.padding = padding;
        paddingValue.textContent = padding;
        this.devFrames.updatePreview();
        this.devFrames.saveSettings();
      });
    }
    
    // Border radius
    const borderRadiusRange = document.getElementById('borderRadiusRange');
    const borderRadiusValue = document.getElementById('borderRadiusValue');
    if (borderRadiusRange && borderRadiusValue) {
      borderRadiusRange.addEventListener('input', (e) => {
        const radius = parseInt(e.target.value);
        this.devFrames.settings.roundedCorners = radius;
        borderRadiusValue.textContent = radius;
        this.devFrames.updatePreview();
        this.devFrames.saveSettings();
      });
    }
    
    // Window style
    const windowStyleSelect = document.getElementById('windowStyleSelect');
    if (windowStyleSelect) {
      windowStyleSelect.addEventListener('change', (e) => {
        this.devFrames.settings.windowStyle = e.target.value;
        this.devFrames.updatePreview();
        this.devFrames.saveSettings();
      });
    }
    
    // Title
    const titleInput = document.getElementById('titleInput');
    if (titleInput) {
      titleInput.addEventListener('input', (e) => {
        this.devFrames.settings.title = e.target.value;
        this.devFrames.updatePreview();
        this.devFrames.saveSettings();
      });
    }
    
    // Line numbers
    const lineNumbersCheck = document.getElementById('lineNumbersCheck');
    if (lineNumbersCheck) {
      lineNumbersCheck.addEventListener('change', (e) => {
        this.devFrames.settings.lineNumbers = e.target.checked;
        this.devFrames.updatePreview();
        this.devFrames.saveSettings();
      });
    }
    
    // Shadow
    const shadowCheck = document.getElementById('shadowCheck');
    if (shadowCheck) {
      shadowCheck.addEventListener('change', (e) => {
        this.devFrames.settings.shadow = e.target.checked;
        this.devFrames.updatePreview();
        this.devFrames.saveSettings();
      });
    }
    
    // Reset settings
    const resetSettings = document.getElementById('resetSettings');
    if (resetSettings) {
      resetSettings.addEventListener('click', () => {
        this.resetToDefaults();
      });
    }
    
    // Save preset
    const savePreset = document.getElementById('savePreset');
    if (savePreset) {
      savePreset.addEventListener('click', () => {
        this.savePreset();
      });
    }
  }
  
  addArduinoSupport() {
    // Add Arduino-specific syntax highlighting
    if (typeof Prism !== 'undefined') {
      Prism.languages.arduino = Prism.languages.extend('cpp', {
        'arduino-function': {
          pattern: /\b(?:setup|loop|pinMode|digitalWrite|digitalRead|analogWrite|analogRead|delay|delayMicroseconds|millis|micros|map|constrain|random|randomSeed|attachInterrupt|detachInterrupt|interrupts|noInterrupts|tone|noTone|shiftOut|shiftIn|pulseIn|pulseInLong)\b/,
          alias: 'function'
        },
        'arduino-constant': {
          pattern: /\b(?:HIGH|LOW|INPUT|OUTPUT|INPUT_PULLUP|LED_BUILTIN|A0|A1|A2|A3|A4|A5|A6|A7)\b/,
          alias: 'constant'
        },
        'arduino-pin': {
          pattern: /\b(?:D\d+|\d+)\b(?=\s*[,)])/,
          alias: 'number'
        },
        'arduino-type': {
          pattern: /\b(?:byte|word|String|boolean)\b/,
          alias: 'type'
        }
      });
    }
  }
  
  handleAutoClosing(e) {
    const codeEditor = document.getElementById('codeEditor');
    if (!codeEditor) return;
    
    const openBrackets = ['(', '[', '{', '"', "'"];
    const closeBrackets = [')', ']', '}', '"', "'"];
    
    if (openBrackets.includes(e.data)) {
      const start = codeEditor.selectionStart;
      const closeBracket = closeBrackets[openBrackets.indexOf(e.data)];
      
      // Insert closing bracket
      codeEditor.value = codeEditor.value.substring(0, start) + 
                        closeBracket + 
                        codeEditor.value.substring(start);
      
      // Move cursor back
      codeEditor.selectionStart = codeEditor.selectionEnd = start;
    }
  }
  
  saveToHistory(content) {
    // Remove future history if we're not at the end
    if (this.historyIndex < this.history.length - 1) {
      this.history = this.history.slice(0, this.historyIndex + 1);
    }
    
    // Add new state
    this.history.push(content);
    this.historyIndex = this.history.length - 1;
    
    // Limit history size
    if (this.history.length > this.maxHistory) {
      this.history.shift();
      this.historyIndex--;
    }
  }
  
  undo() {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      const codeEditor = document.getElementById('codeEditor');
      if (codeEditor) {
        codeEditor.value = this.history[this.historyIndex];
        this.devFrames.updatePreview();
      }
    }
  }
  
  redo() {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      const codeEditor = document.getElementById('codeEditor');
      if (codeEditor) {
        codeEditor.value = this.history[this.historyIndex];
        this.devFrames.updatePreview();
      }
    }
  }
  
  resetToDefaults() {
    this.devFrames.settings = {
      fontSize: 14,
      fontFamily: 'Fira Code',
      padding: 20,
      lineNumbers: true,
      windowStyle: 'macos',
      title: '',
      roundedCorners: 8,
      shadow: true,
      background: 'gradient'
    };
    
    // Update UI controls
    this.updateControlValues();
    this.devFrames.updatePreview();
    this.devFrames.saveSettings();
  }
  
  updateControlValues() {
    const controls = {
      'fontFamilySelect': this.devFrames.settings.fontFamily,
      'fontSizeRange': this.devFrames.settings.fontSize,
      'paddingRange': this.devFrames.settings.padding,
      'borderRadiusRange': this.devFrames.settings.roundedCorners,
      'windowStyleSelect': this.devFrames.settings.windowStyle,
      'titleInput': this.devFrames.settings.title,
      'lineNumbersCheck': this.devFrames.settings.lineNumbers,
      'shadowCheck': this.devFrames.settings.shadow
    };
    
    Object.entries(controls).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element) {
        if (element.type === 'checkbox') {
          element.checked = value;
        } else {
          element.value = value;
        }
      }
    });
    
    // Update value displays
    document.getElementById('fontSizeValue').textContent = this.devFrames.settings.fontSize;
    document.getElementById('paddingValue').textContent = this.devFrames.settings.padding;
    document.getElementById('borderRadiusValue').textContent = this.devFrames.settings.roundedCorners;
  }
  
  savePreset() {
    const presetName = prompt('Enter a name for this preset:');
    if (presetName) {
      const presets = JSON.parse(localStorage.getItem('devframes-presets') || '{}');
      presets[presetName] = { ...this.devFrames.settings };
      localStorage.setItem('devframes-presets', JSON.stringify(presets));
      
      // Show success message
      this.showNotification('Preset saved successfully!', 'success');
    }
  }
  
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: var(--primary-color);
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      z-index: 1000;
      animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }
}

// Initialize editor when DevFrames is ready
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    if (window.devFrames) {
      window.codeEditor = new CodeEditor(window.devFrames);
    }
  }, 100);
});
