# Dev Frames - Beautiful Code Snippet Images

Create stunning, professional code snippet images with unique support for Arduino, ESP8266/ESP32, and all major programming languages.

## 🚀 Features

- **Unique Arduino Support**: The only platform with dedicated Arduino/ESP syntax highlighting
- **Multiple Themes**: Choose from Monokai, Dracula, Solarized, and more
- **Advanced Customization**: Fonts, spacing, window styles, colors, and effects
- **Export Options**: High-quality PNG and scalable SVG exports
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Fast & Lightweight**: No heavy frameworks, pure performance
- **Free & Open**: No registration required, unlimited usage

## 🎨 Supported Languages

- Arduino/ESP8266/ESP32 (Unique Feature!)
- C/C++
- Python
- JavaScript
- HTML/CSS
- Java
- JSON/XML
- And many more...

## 🛠️ Installation & Deployment

### Local Development

1. Clone or download the repository
2. Open `index.html` in your web browser
3. Start creating beautiful code frames!

### Web Hosting

1. Upload all files to your web server
2. Ensure the folder structure is maintained:
   ```
   /
   ├── index.html
   ├── assets/
   │   ├── css/
   │   │   ├── main.css
   │   │   ├── themes.css
   │   │   └── responsive.css
   │   ├── js/
   │   │   ├── main.js
   │   │   ├── editor.js
   │   │   └── export.js
   │   └── images/
   └── README.md
   ```
3. Configure your web server to serve static files
4. Update the AdSense client ID in `index.html` if needed

### CDN Dependencies

The application uses these external CDNs:
- Google Fonts (Inter, Fira Code, JetBrains Mono)
- Font Awesome Icons
- Prism.js for syntax highlighting
- html2canvas for PNG export (loaded dynamically)

## 🎯 Usage

1. **Paste Your Code**: Enter or paste your code in the editor
2. **Auto-Detection**: Language is automatically detected (Arduino, Python, JS, etc.)
3. **Customize**: Use the sidebar to adjust themes, fonts, spacing, and styles
4. **Preview**: See real-time preview of your code frame
5. **Export**: Download as PNG or SVG, or copy embed code

## 🎨 Customization Options

- **Code Themes**: Default, Monokai, Dracula, Solarized Light/Dark
- **Fonts**: Fira Code, JetBrains Mono, Monaco, Consolas, Courier New
- **Window Styles**: macOS, Windows, Minimal, None
- **Spacing**: Adjustable padding and border radius
- **Features**: Line numbers, drop shadows, custom titles

## 📱 Mobile Support

Dev Frames is fully responsive and works great on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

## 🔧 Technical Details

- **Frontend**: Pure HTML5, CSS3, JavaScript (ES6+)
- **Styling**: CSS Variables for theming, Flexbox/Grid layouts
- **Icons**: Font Awesome 6
- **Syntax Highlighting**: Prism.js with custom Arduino support
- **Export**: HTML5 Canvas (PNG) and SVG generation
- **Performance**: Optimized for fast loading and smooth interactions

## 🌟 Why Dev Frames?

- **Perfect for Teaching**: Create clear code examples for tutorials
- **Social Media Ready**: Eye-catching snippets for Twitter, LinkedIn
- **Blog Enhancement**: Beautiful code blocks for technical posts
- **Presentations**: Professional code slides for talks
- **Documentation**: Enhance your project documentation

## 👨‍💻 Developer

**SK Raihan**
- Electronics Engineering Student & Developer
- Founder of SKR Electronics Lab
- Passionate about electronics, programming, and developer tools

### Connect with SK Raihan:
- 📧 Email: <EMAIL>
- 📱 Instagram: [@skr_electronics_lab](https://instagram.com/skr_electronics_lab)
- 🎥 YouTube: [SKR Electronics Lab](https://youtube.com/c/SKRElectronicsLab)
- 🐦 Twitter: [@skrelectronics](https://twitter.com/skrelectronics)
- 🌐 Website: [skrelectronicslab.com](https://skrelectronicslab.com)
- ☕ Support: [Buy Me a Coffee](https://buymeacoffee.com/skrelectronics)

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the issues page.

## 🙏 Acknowledgments

- Prism.js for excellent syntax highlighting
- Font Awesome for beautiful icons
- Google Fonts for typography
- The developer community for inspiration

---

**Made with ❤️ by SK Raihan - SKR Electronics Lab**

Create your first beautiful code frame at [Dev Frames](https://devframes.com)!
