// Dev Frames - Main JavaScript

class DevFrames {
  constructor() {
    this.currentTheme = 'day';
    this.currentCodeTheme = 'default';
    this.currentLanguage = 'auto';
    this.settings = {
      fontSize: 14,
      fontFamily: 'Fira Code',
      padding: 20,
      lineNumbers: true,
      windowStyle: 'macos',
      title: '',
      roundedCorners: 8,
      shadow: true,
      background: 'gradient'
    };
    
    this.init();
  }
  
  init() {
    this.setupEventListeners();
    this.setupThemeToggle();
    this.setupNavigation();
    this.setupMobileMenu();
    this.loadSettings();
    this.loadDefaultCode();
    this.updatePreview();
  }
  
  setupEventListeners() {
    // Code editor events
    const codeEditor = document.getElementById('codeEditor');
    if (codeEditor) {
      codeEditor.addEventListener('input', () => this.handleCodeChange());
      codeEditor.addEventListener('paste', () => {
        setTimeout(() => this.detectLanguage(), 100);
      });
    }
    
    // Language selector
    const languageSelect = document.getElementById('languageSelect');
    if (languageSelect) {
      languageSelect.addEventListener('change', (e) => {
        this.currentLanguage = e.target.value;
        this.updatePreview();
      });
    }
    
    // Editor actions
    const clearBtn = document.getElementById('clearCode');
    const undoBtn = document.getElementById('undoCode');
    const redoBtn = document.getElementById('redoCode');
    
    if (clearBtn) clearBtn.addEventListener('click', () => this.clearCode());
    if (undoBtn) undoBtn.addEventListener('click', () => this.undoCode());
    if (redoBtn) redoBtn.addEventListener('click', () => this.redoCode());
  }
  
  setupThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
      themeToggle.addEventListener('click', () => this.toggleTheme());
    }
    
    // Check for saved theme preference or default to 'day'
    const savedTheme = localStorage.getItem('devframes-theme') || 'day';
    this.setTheme(savedTheme);
  }
  
  setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.section');
    
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').substring(1);
        
        // Update active nav link
        navLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
        
        // Show target section
        sections.forEach(section => {
          section.classList.remove('active');
          if (section.id === targetId) {
            section.classList.add('active');
          }
        });
      });
    });
  }
  
  setupMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileToggle && navMenu) {
      mobileToggle.addEventListener('click', () => {
        mobileToggle.classList.toggle('active');
        navMenu.classList.toggle('active');
      });
    }
  }
  
  toggleTheme() {
    const newTheme = this.currentTheme === 'day' ? 'night' : 'day';
    this.setTheme(newTheme);
  }
  
  setTheme(theme) {
    this.currentTheme = theme;
    document.body.className = `theme-${theme}`;
    
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
      const icon = themeToggle.querySelector('i');
      if (icon) {
        icon.className = theme === 'day' ? 'fas fa-moon' : 'fas fa-sun';
      }
    }
    
    localStorage.setItem('devframes-theme', theme);
  }
  
  handleCodeChange() {
    const codeEditor = document.getElementById('codeEditor');
    if (codeEditor) {
      if (this.currentLanguage === 'auto') {
        this.detectLanguage();
      }
      this.updatePreview();
    }
  }
  
  detectLanguage() {
    const codeEditor = document.getElementById('codeEditor');
    if (!codeEditor) return;
    
    const code = codeEditor.value.trim();
    if (!code) return;
    
    let detectedLanguage = 'text';
    
    // Arduino/ESP detection
    if (this.isArduinoCode(code)) {
      detectedLanguage = 'arduino';
    }
    // C++ detection
    else if (code.includes('#include') && (code.includes('std::') || code.includes('cout') || code.includes('cin'))) {
      detectedLanguage = 'cpp';
    }
    // C detection
    else if (code.includes('#include') && code.includes('printf')) {
      detectedLanguage = 'c';
    }
    // Python detection
    else if (code.includes('def ') || code.includes('import ') || code.includes('print(')) {
      detectedLanguage = 'python';
    }
    // JavaScript detection
    else if (code.includes('function') || code.includes('const ') || code.includes('let ') || code.includes('var ')) {
      detectedLanguage = 'javascript';
    }
    // HTML detection
    else if (code.includes('<!DOCTYPE') || code.includes('<html') || code.includes('<div')) {
      detectedLanguage = 'html';
    }
    // CSS detection
    else if (code.includes('{') && code.includes(':') && code.includes(';')) {
      detectedLanguage = 'css';
    }
    // JSON detection
    else if (code.startsWith('{') || code.startsWith('[')) {
      try {
        JSON.parse(code);
        detectedLanguage = 'json';
      } catch (e) {
        // Not valid JSON
      }
    }
    
    // Update language selector
    const languageSelect = document.getElementById('languageSelect');
    if (languageSelect && detectedLanguage !== 'text') {
      languageSelect.value = detectedLanguage;
      this.currentLanguage = detectedLanguage;
    }
  }
  
  isArduinoCode(code) {
    const arduinoKeywords = [
      'void setup()', 'void loop()', 'pinMode', 'digitalWrite', 'digitalRead',
      'analogWrite', 'analogRead', 'Serial.begin', 'Serial.print', 'delay',
      'HIGH', 'LOW', 'INPUT', 'OUTPUT', 'INPUT_PULLUP',
      '#include <WiFi.h>', '#include <ESP8266WiFi.h>', 'WiFi.begin',
      'ESP.restart', 'ESP.deepSleep'
    ];
    
    return arduinoKeywords.some(keyword => code.includes(keyword));
  }
  
  updatePreview() {
    const codeEditor = document.getElementById('codeEditor');
    const preview = document.getElementById('codePreview');
    
    if (!codeEditor || !preview) return;
    
    const code = codeEditor.value || this.getPlaceholderCode();
    const language = this.currentLanguage === 'auto' ? 'text' : this.currentLanguage;
    
    // Create frame structure
    const frameHTML = this.generateFrameHTML(code, language);
    preview.innerHTML = frameHTML;
    
    // Apply syntax highlighting
    this.applySyntaxHighlighting();
  }
  
  generateFrameHTML(code, language) {
    const { windowStyle, title, lineNumbers } = this.settings;
    
    let frameClass = `code-frame theme-${this.currentCodeTheme}`;
    let headerHTML = '';
    
    // Generate window header based on style
    if (windowStyle !== 'none') {
      headerHTML = this.generateWindowHeader(windowStyle, title);
    }
    
    // Generate line numbers if enabled
    const codeLines = code.split('\n');
    let codeHTML = '';
    
    if (lineNumbers) {
      codeHTML = codeLines.map((line, index) => 
        `<span class="line-number">${index + 1}</span><span class="line-content">${this.escapeHtml(line)}</span>`
      ).join('\n');
    } else {
      codeHTML = this.escapeHtml(code);
    }
    
    return `
      <div class="${frameClass}" style="
        padding: ${this.settings.padding}px;
        border-radius: ${this.settings.roundedCorners}px;
        font-family: ${this.settings.fontFamily};
        font-size: ${this.settings.fontSize}px;
        ${this.settings.shadow ? 'box-shadow: 0 8px 16px rgba(0,0,0,0.15);' : ''}
      ">
        ${headerHTML}
        <div class="frame-content">
          <pre><code class="language-${language}">${codeHTML}</code></pre>
        </div>
      </div>
    `;
  }
  
  generateWindowHeader(style, title) {
    const displayTitle = title || this.getLanguageDisplayName();
    
    if (style === 'macos') {
      return `
        <div class="frame-header">
          <div class="frame-controls">
            <div class="frame-control close"></div>
            <div class="frame-control minimize"></div>
            <div class="frame-control maximize"></div>
          </div>
          <div class="frame-title">${displayTitle}</div>
        </div>
      `;
    } else if (style === 'windows') {
      return `
        <div class="frame-header windows">
          <div class="frame-title">${displayTitle}</div>
          <div class="frame-controls">
            <div class="frame-control minimize">−</div>
            <div class="frame-control maximize">□</div>
            <div class="frame-control close">×</div>
          </div>
        </div>
      `;
    }
    
    return '';
  }
  
  getLanguageDisplayName() {
    const languageNames = {
      'arduino': 'Arduino Code',
      'cpp': 'C++',
      'c': 'C',
      'python': 'Python',
      'javascript': 'JavaScript',
      'html': 'HTML',
      'css': 'CSS',
      'java': 'Java',
      'json': 'JSON',
      'xml': 'XML'
    };
    
    return languageNames[this.currentLanguage] || 'Code';
  }
  
  getPlaceholderCode() {
    const placeholders = {
      'arduino': `// Arduino LED Blink Example
void setup() {
  Serial.begin(9600);
  pinMode(LED_BUILTIN, OUTPUT);
  Serial.println("Arduino Dev Frames Demo");
}

void loop() {
  digitalWrite(LED_BUILTIN, HIGH);
  delay(1000);
  digitalWrite(LED_BUILTIN, LOW);
  delay(1000);
}`,
      'python': `def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Generate first 10 Fibonacci numbers
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")`,
      'javascript': `function createCounter() {
  let count = 0;
  
  return {
    increment: () => ++count,
    decrement: () => --count,
    value: () => count
  };
}

const counter = createCounter();
console.log(counter.increment()); // 1`
    };
    
    return placeholders[this.currentLanguage] || placeholders['arduino'];
  }
  
  applySyntaxHighlighting() {
    if (typeof Prism !== 'undefined') {
      Prism.highlightAll();
    }
  }
  
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
  
  clearCode() {
    const codeEditor = document.getElementById('codeEditor');
    if (codeEditor) {
      codeEditor.value = '';
      this.updatePreview();
    }
  }
  
  undoCode() {
    // Basic undo functionality
    document.execCommand('undo');
  }
  
  redoCode() {
    // Basic redo functionality
    document.execCommand('redo');
  }
  
  loadSettings() {
    const savedSettings = localStorage.getItem('devframes-settings');
    if (savedSettings) {
      this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
    }
  }
  
  saveSettings() {
    localStorage.setItem('devframes-settings', JSON.stringify(this.settings));
  }

  loadDefaultCode() {
    const codeEditor = document.getElementById('codeEditor');
    if (codeEditor && !codeEditor.value.trim()) {
      codeEditor.value = this.getPlaceholderCode();
      this.currentLanguage = 'arduino';
      const languageSelect = document.getElementById('languageSelect');
      if (languageSelect) {
        languageSelect.value = 'arduino';
      }
    }
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.devFrames = new DevFrames();
});
