/* Dev Frames - Code Editor Themes */

/* Base Code Styling */
.code-preview pre,
.code-editor {
  font-family: var(--font-family-code);
  font-size: 14px;
  line-height: 1.5;
  tab-size: 2;
  -moz-tab-size: 2;
}

/* Theme: Default Light */
.theme-default {
  background-color: #fafafa;
  color: #383a42;
}

.theme-default .token.comment,
.theme-default .token.prolog,
.theme-default .token.doctype,
.theme-default .token.cdata {
  color: #a0a1a7;
  font-style: italic;
}

.theme-default .token.punctuation {
  color: #383a42;
}

.theme-default .token.property,
.theme-default .token.tag,
.theme-default .token.boolean,
.theme-default .token.number,
.theme-default .token.constant,
.theme-default .token.symbol,
.theme-default .token.deleted {
  color: #e45649;
}

.theme-default .token.selector,
.theme-default .token.attr-name,
.theme-default .token.string,
.theme-default .token.char,
.theme-default .token.builtin,
.theme-default .token.inserted {
  color: #50a14f;
}

.theme-default .token.operator,
.theme-default .token.entity,
.theme-default .token.url,
.theme-default .language-css .token.string,
.theme-default .style .token.string {
  color: #0184bc;
}

.theme-default .token.atrule,
.theme-default .token.attr-value,
.theme-default .token.keyword {
  color: #a626a4;
}

.theme-default .token.function,
.theme-default .token.class-name {
  color: #c18401;
}

/* Theme: Monokai */
.theme-monokai {
  background-color: #272822;
  color: #f8f8f2;
}

.theme-monokai .token.comment,
.theme-monokai .token.prolog,
.theme-monokai .token.doctype,
.theme-monokai .token.cdata {
  color: #75715e;
  font-style: italic;
}

.theme-monokai .token.punctuation {
  color: #f8f8f2;
}

.theme-monokai .token.property,
.theme-monokai .token.tag,
.theme-monokai .token.constant,
.theme-monokai .token.symbol,
.theme-monokai .token.deleted {
  color: #f92672;
}

.theme-monokai .token.boolean,
.theme-monokai .token.number {
  color: #ae81ff;
}

.theme-monokai .token.selector,
.theme-monokai .token.attr-name,
.theme-monokai .token.string,
.theme-monokai .token.char,
.theme-monokai .token.builtin,
.theme-monokai .token.inserted {
  color: #a6e22e;
}

.theme-monokai .token.operator,
.theme-monokai .token.entity,
.theme-monokai .token.url,
.theme-monokai .language-css .token.string,
.theme-monokai .style .token.string,
.theme-monokai .token.variable {
  color: #f8f8f2;
}

.theme-monokai .token.atrule,
.theme-monokai .token.attr-value,
.theme-monokai .token.function,
.theme-monokai .token.class-name {
  color: #e6db74;
}

.theme-monokai .token.keyword {
  color: #66d9ef;
}

/* Theme: Dracula */
.theme-dracula {
  background-color: #282a36;
  color: #f8f8f2;
}

.theme-dracula .token.comment,
.theme-dracula .token.prolog,
.theme-dracula .token.doctype,
.theme-dracula .token.cdata {
  color: #6272a4;
  font-style: italic;
}

.theme-dracula .token.punctuation {
  color: #f8f8f2;
}

.theme-dracula .token.property,
.theme-dracula .token.tag,
.theme-dracula .token.constant,
.theme-dracula .token.symbol,
.theme-dracula .token.deleted {
  color: #ff79c6;
}

.theme-dracula .token.boolean,
.theme-dracula .token.number {
  color: #bd93f9;
}

.theme-dracula .token.selector,
.theme-dracula .token.attr-name,
.theme-dracula .token.string,
.theme-dracula .token.char,
.theme-dracula .token.builtin,
.theme-dracula .token.inserted {
  color: #f1fa8c;
}

.theme-dracula .token.operator,
.theme-dracula .token.entity,
.theme-dracula .token.url,
.theme-dracula .language-css .token.string,
.theme-dracula .style .token.string,
.theme-dracula .token.variable {
  color: #f8f8f2;
}

.theme-dracula .token.atrule,
.theme-dracula .token.attr-value,
.theme-dracula .token.function,
.theme-dracula .token.class-name {
  color: #50fa7b;
}

.theme-dracula .token.keyword {
  color: #ff79c6;
}

/* Theme: Solarized Light */
.theme-solarized-light {
  background-color: #fdf6e3;
  color: #657b83;
}

.theme-solarized-light .token.comment,
.theme-solarized-light .token.prolog,
.theme-solarized-light .token.doctype,
.theme-solarized-light .token.cdata {
  color: #93a1a1;
  font-style: italic;
}

.theme-solarized-light .token.punctuation {
  color: #586e75;
}

.theme-solarized-light .token.property,
.theme-solarized-light .token.tag,
.theme-solarized-light .token.boolean,
.theme-solarized-light .token.number,
.theme-solarized-light .token.constant,
.theme-solarized-light .token.symbol,
.theme-solarized-light .token.deleted {
  color: #dc322f;
}

.theme-solarized-light .token.selector,
.theme-solarized-light .token.attr-name,
.theme-solarized-light .token.string,
.theme-solarized-light .token.char,
.theme-solarized-light .token.builtin,
.theme-solarized-light .token.inserted {
  color: #859900;
}

.theme-solarized-light .token.operator,
.theme-solarized-light .token.entity,
.theme-solarized-light .token.url,
.theme-solarized-light .language-css .token.string,
.theme-solarized-light .style .token.string {
  color: #657b83;
}

.theme-solarized-light .token.atrule,
.theme-solarized-light .token.attr-value,
.theme-solarized-light .token.keyword {
  color: #859900;
}

.theme-solarized-light .token.function,
.theme-solarized-light .token.class-name {
  color: #b58900;
}

/* Theme: Solarized Dark */
.theme-solarized-dark {
  background-color: #002b36;
  color: #839496;
}

.theme-solarized-dark .token.comment,
.theme-solarized-dark .token.prolog,
.theme-solarized-dark .token.doctype,
.theme-solarized-dark .token.cdata {
  color: #586e75;
  font-style: italic;
}

.theme-solarized-dark .token.punctuation {
  color: #93a1a1;
}

.theme-solarized-dark .token.property,
.theme-solarized-dark .token.tag,
.theme-solarized-dark .token.boolean,
.theme-solarized-dark .token.number,
.theme-solarized-dark .token.constant,
.theme-solarized-dark .token.symbol,
.theme-solarized-dark .token.deleted {
  color: #dc322f;
}

.theme-solarized-dark .token.selector,
.theme-solarized-dark .token.attr-name,
.theme-solarized-dark .token.string,
.theme-solarized-dark .token.char,
.theme-solarized-dark .token.builtin,
.theme-solarized-dark .token.inserted {
  color: #859900;
}

.theme-solarized-dark .token.operator,
.theme-solarized-dark .token.entity,
.theme-solarized-dark .token.url,
.theme-solarized-dark .language-css .token.string,
.theme-solarized-dark .style .token.string {
  color: #839496;
}

.theme-solarized-dark .token.atrule,
.theme-solarized-dark .token.attr-value,
.theme-solarized-dark .token.keyword {
  color: #859900;
}

.theme-solarized-dark .token.function,
.theme-solarized-dark .token.class-name {
  color: #b58900;
}

/* Arduino/ESP Specific Highlighting */
.token.arduino-function {
  color: #d19a66 !important;
  font-weight: 600;
}

.token.arduino-constant {
  color: #56b6c2 !important;
  font-weight: 600;
}

.token.arduino-pin {
  color: #e06c75 !important;
  font-weight: 600;
}

.token.arduino-type {
  color: #61afef !important;
  font-weight: 600;
}
