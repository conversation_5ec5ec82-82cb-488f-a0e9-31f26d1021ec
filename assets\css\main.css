/* Dev Frames - Main Styles */

/* CSS Variables for Theme Support */
:root {
  /* Day Theme Colors (Warm Palette) */
  --primary-color: #f59e0b;
  --primary-hover: #d97706;
  --secondary-color: #f3f4f6;
  --accent-color: #fbbf24;
  
  --bg-primary: #fefbf3;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f9f5f0;
  --bg-code: #faf8f5;
  
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --text-inverse: #ffffff;
  
  --border-color: #e5e7eb;
  --border-hover: #d1d5db;
  --shadow-light: rgba(0, 0, 0, 0.05);
  --shadow-medium: rgba(0, 0, 0, 0.1);
  --shadow-heavy: rgba(0, 0, 0, 0.15);
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON>o, sans-serif;
  --font-family-code: 'Fira Code', 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Transitions */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme Variables */
.theme-night {
  --primary-color: #06b6d4;
  --primary-hover: #0891b2;
  --secondary-color: #374151;
  --accent-color: #22d3ee;
  
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --bg-code: #1e293b;
  
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --text-inverse: #111827;
  
  --border-color: #374151;
  --border-hover: #4b5563;
  --shadow-light: rgba(0, 0, 0, 0.2);
  --shadow-medium: rgba(0, 0, 0, 0.3);
  --shadow-heavy: rgba(0, 0, 0, 0.4);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Navigation */
.navbar {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all var(--transition-normal);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  transition: color var(--transition-fast);
}

.brand-link:hover {
  color: var(--primary-color);
}

.brand-link i {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  background-color: var(--bg-tertiary);
}

.theme-toggle {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.theme-toggle:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: var(--bg-tertiary);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
}

.mobile-menu-toggle span {
  width: 24px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all var(--transition-fast);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-xl) 0;
}

.section {
  display: none;
}

.section.active {
  display: block;
}

/* Hero Section */
.hero {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* App Layout */
.app-layout {
  display: grid;
  grid-template-columns: 1fr 300px 1fr;
  gap: var(--spacing-xl);
  align-items: start;
}

/* Panel Styles */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.panel-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.panel-title i {
  color: var(--primary-color);
}

/* Editor Panel */
.editor-panel {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 6px var(--shadow-light);
}

.editor-container {
  position: relative;
}

.code-editor {
  width: 100%;
  min-height: 400px;
  padding: var(--spacing-md);
  border: none;
  background-color: var(--bg-code);
  color: var(--text-primary);
  font-family: var(--font-family-code);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  resize: vertical;
  outline: none;
  border-radius: 0;
}

.editor-footer {
  padding: var(--spacing-md);
  background-color: var(--bg-tertiary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.language-select {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.language-select:hover,
.language-select:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px var(--shadow-medium);
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-secondary);
  border-color: var(--border-hover);
}

.editor-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.preview-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Customization Panel */
.customization-panel {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 6px var(--shadow-light);
  position: sticky;
  top: 80px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}

.customization-content {
  padding: var(--spacing-md);
}

.control-group {
  margin-bottom: var(--spacing-lg);
}

.control-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.control-input {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background-color: var(--bg-code);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.control-input:hover,
.control-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.control-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--bg-tertiary);
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.control-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.control-range::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px var(--shadow-medium);
}

.control-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  transition: all var(--transition-fast);
}

.control-checkbox {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
}

.control-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

/* Preview Panel */
.preview-panel {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 6px var(--shadow-light);
}

.preview-container {
  padding: var(--spacing-xl);
  background-color: var(--bg-tertiary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-preview {
  background-color: var(--bg-code);
  border-radius: var(--radius-lg);
  box-shadow: 0 8px 16px var(--shadow-medium);
  overflow: hidden;
  max-width: 100%;
  transition: all var(--transition-normal);
}

.code-frame {
  position: relative;
}

.frame-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, #ff6b6b, #feca57, #48dbfb);
  gap: var(--spacing-sm);
}

.frame-controls {
  display: flex;
  gap: var(--spacing-xs);
}

.frame-control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.frame-control.close {
  background-color: #ff5f56;
}

.frame-control.minimize {
  background-color: #ffbd2e;
}

.frame-control.maximize {
  background-color: #27ca3f;
}

.frame-title {
  flex: 1;
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-inverse);
}

.frame-content {
  padding: var(--spacing-lg);
  background-color: var(--bg-code);
}

.frame-content pre {
  margin: 0;
  font-family: var(--font-family-code);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--text-primary);
  overflow-x: auto;
}

/* Footer */
.footer {
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: var(--spacing-xl) 0;
  margin-top: auto;
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.footer-brand i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.footer-links {
  display: flex;
  gap: var(--spacing-md);
}

.footer-links a {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
}

.footer-bottom p {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.footer-bottom a {
  color: var(--primary-color);
  text-decoration: none;
}

.footer-bottom a:hover {
  text-decoration: underline;
}

/* About Page Styles */
.about-content {
  max-width: 1000px;
  margin: 0 auto;
}

.about-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.about-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.about-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

.about-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-3xl);
}

.about-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-normal);
  box-shadow: 0 2px 4px var(--shadow-light);
}

.about-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px var(--shadow-medium);
  border-color: var(--primary-color);
}

.card-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
}

.card-icon i {
  font-size: var(--font-size-2xl);
  color: var(--text-inverse);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.card-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Developer Section */
.developer-section {
  margin-bottom: var(--spacing-3xl);
}

.developer-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
  display: flex;
  gap: var(--spacing-xl);
  align-items: center;
  box-shadow: 0 4px 6px var(--shadow-light);
}

.developer-avatar {
  flex-shrink: 0;
}

.developer-avatar i {
  font-size: 80px;
  color: var(--primary-color);
}

.developer-name {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.developer-title {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-md);
}

.developer-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.developer-links {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.developer-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.developer-link:hover {
  background-color: var(--primary-color);
  color: var(--text-inverse);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.developer-link.support-link {
  background-color: var(--accent-color);
  color: var(--text-inverse);
  border-color: var(--accent-color);
}

.developer-link.support-link:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

/* Why Section */
.why-section {
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.why-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.why-item {
  text-align: center;
  padding: var(--spacing-lg);
}

.why-item h3 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.why-item p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: var(--text-inverse);
  padding: var(--spacing-3xl);
  border-radius: var(--radius-xl);
  margin-top: var(--spacing-3xl);
}

.cta-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
}

.cta-description {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}
