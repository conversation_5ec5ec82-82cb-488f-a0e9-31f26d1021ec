// Dev Frames - Export Functionality

class ExportManager {
  constructor(devFrames) {
    this.devFrames = devFrames;
    this.init();
  }
  
  init() {
    this.setupExportButtons();
  }
  
  setupExportButtons() {
    const exportPNG = document.getElementById('exportPNG');
    const exportSVG = document.getElementById('exportSVG');
    
    if (exportPNG) {
      exportPNG.addEventListener('click', () => this.exportAsPNG());
    }
    
    if (exportSVG) {
      exportSVG.addEventListener('click', () => this.exportAsSVG());
    }
  }
  
  async exportAsPNG() {
    try {
      const preview = document.getElementById('codePreview');
      if (!preview) {
        this.showError('Preview not found');
        return;
      }
      
      // Show loading state
      this.setExportLoading('exportPNG', true);
      
      // Create a temporary container with higher resolution
      const tempContainer = this.createHighResContainer(preview);
      document.body.appendChild(tempContainer);
      
      // Use html2canvas to capture the element
      const canvas = await this.captureElement(tempContainer);
      
      // Clean up
      document.body.removeChild(tempContainer);
      
      // Download the image
      this.downloadCanvas(canvas, 'dev-frame.png');
      
      this.setExportLoading('exportPNG', false);
      this.showSuccess('PNG exported successfully!');
      
    } catch (error) {
      console.error('PNG export failed:', error);
      this.setExportLoading('exportPNG', false);
      this.showError('Failed to export PNG');
    }
  }
  
  async exportAsSVG() {
    try {
      const preview = document.getElementById('codePreview');
      if (!preview) {
        this.showError('Preview not found');
        return;
      }
      
      this.setExportLoading('exportSVG', true);
      
      const svgContent = this.generateSVG(preview);
      this.downloadSVG(svgContent, 'dev-frame.svg');
      
      this.setExportLoading('exportSVG', false);
      this.showSuccess('SVG exported successfully!');
      
    } catch (error) {
      console.error('SVG export failed:', error);
      this.setExportLoading('exportSVG', false);
      this.showError('Failed to export SVG');
    }
  }
  
  createHighResContainer(element) {
    const container = element.cloneNode(true);
    const scale = 2; // 2x resolution for better quality
    
    container.style.cssText = `
      position: absolute;
      top: -9999px;
      left: -9999px;
      transform: scale(${scale});
      transform-origin: top left;
      background: transparent;
      padding: 40px;
    `;
    
    return container;
  }
  
  async captureElement(element) {
    // Check if html2canvas is available
    if (typeof html2canvas === 'undefined') {
      // Fallback: load html2canvas dynamically
      await this.loadHtml2Canvas();
    }
    
    return html2canvas(element, {
      backgroundColor: null,
      scale: 2,
      useCORS: true,
      allowTaint: true,
      foreignObjectRendering: true,
      logging: false
    });
  }
  
  async loadHtml2Canvas() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
  
  generateSVG(element) {
    const rect = element.getBoundingClientRect();
    const styles = window.getComputedStyle(element);
    
    // Get the code content
    const codeElement = element.querySelector('pre code');
    const code = codeElement ? codeElement.textContent : '';
    
    // Create SVG structure
    const svgWidth = Math.max(600, rect.width + 80);
    const svgHeight = Math.max(400, rect.height + 80);
    
    const svgContent = `
      <svg xmlns="http://www.w3.org/2000/svg" width="${svgWidth}" height="${svgHeight}" viewBox="0 0 ${svgWidth} ${svgHeight}">
        <defs>
          <style>
            .code-frame {
              font-family: ${this.devFrames.settings.fontFamily}, monospace;
              font-size: ${this.devFrames.settings.fontSize}px;
            }
            .frame-bg {
              fill: ${this.getThemeColor('bg-code')};
              stroke: ${this.getThemeColor('border-color')};
              stroke-width: 1;
            }
            .frame-header {
              fill: url(#headerGradient);
            }
            .frame-text {
              fill: ${this.getThemeColor('text-primary')};
              font-family: ${this.devFrames.settings.fontFamily}, monospace;
              font-size: ${this.devFrames.settings.fontSize}px;
            }
            .line-number {
              fill: ${this.getThemeColor('text-muted')};
              font-size: ${this.devFrames.settings.fontSize - 2}px;
            }
          </style>
          <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#feca57;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#48dbfb;stop-opacity:1" />
          </linearGradient>
        </defs>
        
        <!-- Frame Background -->
        <rect class="frame-bg" x="40" y="40" width="${svgWidth - 80}" height="${svgHeight - 80}" 
              rx="${this.devFrames.settings.roundedCorners}" ry="${this.devFrames.settings.roundedCorners}"/>
        
        <!-- Header -->
        ${this.generateSVGHeader(svgWidth)}
        
        <!-- Code Content -->
        ${this.generateSVGCode(code, svgWidth, svgHeight)}
      </svg>
    `;
    
    return svgContent;
  }
  
  generateSVGHeader(width) {
    if (this.devFrames.settings.windowStyle === 'none') return '';
    
    const headerHeight = 40;
    const title = this.devFrames.settings.title || this.devFrames.getLanguageDisplayName();
    
    return `
      <!-- Header Background -->
      <rect class="frame-header" x="40" y="40" width="${width - 80}" height="${headerHeight}" 
            rx="${this.devFrames.settings.roundedCorners}" ry="${this.devFrames.settings.roundedCorners}"/>
      
      <!-- Window Controls -->
      <circle cx="60" cy="60" r="6" fill="#ff5f56"/>
      <circle cx="80" cy="60" r="6" fill="#ffbd2e"/>
      <circle cx="100" cy="60" r="6" fill="#27ca3f"/>
      
      <!-- Title -->
      <text x="${width / 2}" y="65" text-anchor="middle" class="frame-text" fill="white" font-weight="600">
        ${this.escapeXml(title)}
      </text>
    `;
  }
  
  generateSVGCode(code, width, height) {
    const lines = code.split('\n');
    const lineHeight = this.devFrames.settings.fontSize * 1.5;
    const startY = this.devFrames.settings.windowStyle === 'none' ? 60 : 100;
    const padding = this.devFrames.settings.padding;
    
    let svgCode = '';
    
    lines.forEach((line, index) => {
      const y = startY + (index * lineHeight);
      
      if (this.devFrames.settings.lineNumbers) {
        // Line number
        svgCode += `<text x="${60}" y="${y}" class="line-number">${index + 1}</text>`;
        // Code line
        svgCode += `<text x="${90}" y="${y}" class="frame-text">${this.escapeXml(line)}</text>`;
      } else {
        // Code line without line numbers
        svgCode += `<text x="${60}" y="${y}" class="frame-text">${this.escapeXml(line)}</text>`;
      }
    });
    
    return svgCode;
  }
  
  getThemeColor(colorVar) {
    const computedStyle = getComputedStyle(document.documentElement);
    return computedStyle.getPropertyValue(`--${colorVar}`).trim() || '#000000';
  }
  
  downloadCanvas(canvas, filename) {
    const link = document.createElement('a');
    link.download = filename;
    link.href = canvas.toDataURL('image/png');
    link.click();
  }
  
  downloadSVG(svgContent, filename) {
    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.download = filename;
    link.href = url;
    link.click();
    
    URL.revokeObjectURL(url);
  }
  
  setExportLoading(buttonId, loading) {
    const button = document.getElementById(buttonId);
    if (!button) return;
    
    const icon = button.querySelector('i');
    const text = button.querySelector('span') || button.childNodes[button.childNodes.length - 1];
    
    if (loading) {
      button.disabled = true;
      if (icon) icon.className = 'fas fa-spinner fa-spin';
      if (text) text.textContent = ' Exporting...';
    } else {
      button.disabled = false;
      if (icon) {
        icon.className = buttonId === 'exportPNG' ? 'fas fa-download' : 'fas fa-vector-square';
      }
      if (text) {
        text.textContent = buttonId === 'exportPNG' ? ' PNG' : ' SVG';
      }
    }
  }
  
  showSuccess(message) {
    this.showNotification(message, 'success');
  }
  
  showError(message) {
    this.showNotification(message, 'error');
  }
  
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    const bgColor = type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6';
    
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${bgColor};
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      z-index: 1000;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      animation: slideInRight 0.3s ease-out;
    `;
    
    // Add animation styles
    if (!document.getElementById('notification-styles')) {
      const style = document.createElement('style');
      style.id = 'notification-styles';
      style.textContent = `
        @keyframes slideInRight {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          document.body.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }
  
  escapeXml(text) {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
  
  // Generate embed code for sharing
  generateEmbedCode() {
    const preview = document.getElementById('codePreview');
    if (!preview) return '';
    
    const settings = this.devFrames.settings;
    const code = document.getElementById('codeEditor').value;
    const language = this.devFrames.currentLanguage;
    
    return `<!-- Dev Frames Embed -->
<div style="
  font-family: ${settings.fontFamily}, monospace;
  font-size: ${settings.fontSize}px;
  padding: ${settings.padding}px;
  background: var(--bg-code);
  border-radius: ${settings.roundedCorners}px;
  ${settings.shadow ? 'box-shadow: 0 8px 16px rgba(0,0,0,0.15);' : ''}
  overflow-x: auto;
">
  <pre><code class="language-${language}">${this.escapeHtml(code)}</code></pre>
</div>
<!-- Powered by Dev Frames - https://devframes.com -->`;
  }
  
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// Initialize export manager when DevFrames is ready
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    if (window.devFrames) {
      window.exportManager = new ExportManager(window.devFrames);
    }
  }, 100);
});
